-- pin_module.lua - GPIO pin control functionality for Air780EG (LuatOS)

-- No module imports needed
-- Using the global gpio object that is available in LuatOS

local PinModule = {}

-- Initialize pins
function PinModule.setupPins()
    -- Define pin numbers based on the Air780EG schematic
    PinModule.pins = {
        -- Inputs
        S1 = 24,   -- Input signal 1 (GPIO 24)
        S2 = 2,   -- Input signal 2 (GPIO 2)
        S3 = 16,   -- Input signal 3 (GPIO 16)

        -- Outputs
        Relay1 = 29,  -- Relay 1 control (GPIO 29)
        Relay2 = 30, -- Relay 2 control (GPIO 30)
        Relay3 = 31, -- Relay 3 control (GPIO 31)
        Beep = 17,   -- Passive Buzzer/Beeper (GPIO 17 - custom software PWM)
        KeyPower = 10, -- Key/Button for Power (GPIO 10)
        Key1 = 8,   -- Key/Button 1 (GPIO 8)
        Key2 = 11,   -- Key/Button 2 (GPIO 11)

        -- Status LEDs
        -- Using available GPIO pins for LEDs
        CloudLED = 1, -- MQTT connection status LED (GPIO 1)
        NetLED = 9   -- Network status LED (GPIO 9)
    }

    -- Setup input pins with pull-up
    gpio.setup(PinModule.pins.S1, nil, gpio.PULLUP)  -- Input with pull-up
    gpio.setup(PinModule.pins.S2, nil, gpio.PULLUP)  -- Input with pull-up
    gpio.setup(PinModule.pins.S3, nil, gpio.PULLUP)  -- Input with pull-up

    -- Set all outputs to low initially
    PinModule.relayControl("Relay1", 0)
    PinModule.relayControl("Relay2", 0)
    PinModule.relayControl("Relay3", 0)
    PinModule.relayControl("KeyPower", 0)
    PinModule.relayControl("Key1", 0)
    PinModule.relayControl("Key2", 0)
    -- Initialize passive buzzer (PWM4) - ensure it's off initially
    -- No need to initialize PWM here, it will be done when needed

    -- Initialize status LEDs (off initially)
    PinModule.relayControl("CloudLED", 0)
    PinModule.relayControl("NetLED", 0)
end

-- Control relays or outputs
function PinModule.relayControl(pinName, state)
    local pinNum = PinModule.pins[pinName]
    if pinNum then
        -- Set pin as output with the specified state (0 or 1)
        gpio.setup(pinNum, state)
    else
        print("Error: Pin " .. pinName .. " is not defined.")
    end
end

-- Read pin state
function PinModule.readPinState(pinName)
    local pinNum = PinModule.pins[pinName]
    if pinNum then
        return gpio.get(pinNum)
    else
        print("Error: Pin " .. pinName .. " is not defined.")
        return nil
    end
end

-- Control Cloud LED
function PinModule.setCloudLED(state)
    PinModule.relayControl("CloudLED", state)
end

-- Control Network LED
function PinModule.setNetLED(state)
    PinModule.relayControl("NetLED", state)
end

-- Global variable to control software PWM
PinModule.beep_running = false

-- Enhanced Software PWM implementation for passive buzzer on pin 17
function PinModule.softwarePWM(frequency, duration_ms, duty_cycle)
    frequency = frequency or 2000  -- Default 2kHz frequency (higher frequency = louder for passive buzzers)
    duration_ms = duration_ms or 300  -- Default 300ms duration (longer = more audible)
    duty_cycle = duty_cycle or 50  -- Default 50% duty cycle

    -- Calculate timing for software PWM
    local period_us = math.floor(1000000 / frequency)  -- Period in microseconds
    local high_time_us = math.floor(period_us * duty_cycle / 100)  -- High time in microseconds
    local low_time_us = period_us - high_time_us  -- Low time in microseconds

    -- For better audio quality, use smaller time increments when possible
    -- Convert to milliseconds but use minimum practical values
    local high_time_ms = math.max(1, math.floor(high_time_us / 1000))
    local low_time_ms = math.max(1, math.floor(low_time_us / 1000))

    -- For higher frequencies, we need faster switching
    -- If the calculated time is less than 1ms, use rapid switching
    if high_time_us < 1000 or low_time_us < 1000 then
        -- Use rapid switching for high frequencies
        local total_cycles = math.floor(duration_ms * frequency / 1000)

        -- Set pin as output
        gpio.setup(PinModule.pins.Beep, 0)

        PinModule.beep_running = true
        for i = 1, total_cycles do
            if not PinModule.beep_running then
                break
            end

            -- Rapid switching without sys.wait for better frequency accuracy
            gpio.set(PinModule.pins.Beep, 1)
            -- Small delay loop for high phase
            for j = 1, 50 do end  -- Busy wait for microsecond-level timing

            gpio.set(PinModule.pins.Beep, 0)
            -- Small delay loop for low phase
            for j = 1, 50 do end  -- Busy wait for microsecond-level timing

            -- Every 100 cycles, yield to system
            if i % 100 == 0 and sys and sys.wait then
                sys.wait(1)
            end
        end
    else
        -- Use normal timing for lower frequencies
        local cycle_time_ms = high_time_ms + low_time_ms
        local cycles = math.floor(duration_ms / cycle_time_ms)

        -- Set pin as output
        gpio.setup(PinModule.pins.Beep, 0)

        PinModule.beep_running = true
        for i = 1, cycles do
            if not PinModule.beep_running then
                break
            end

            -- High phase
            gpio.set(PinModule.pins.Beep, 1)
            if sys and sys.wait then
                sys.wait(high_time_ms)
            end

            -- Low phase
            gpio.set(PinModule.pins.Beep, 0)
            if sys and sys.wait then
                sys.wait(low_time_ms)
            end
        end
    end

    -- Ensure pin is low when done
    gpio.set(PinModule.pins.Beep, 0)
    PinModule.beep_running = false
end

-- Simple beep function using software PWM
function PinModule.beep(frequency, duration_ms)
    frequency = frequency or 2000  -- Default 2kHz frequency (louder for passive buzzers)
    duration_ms = duration_ms or 300  -- Default 300ms beep (longer = more audible)

    PinModule.softwarePWM(frequency, duration_ms, 50)
end

-- Turn off beeper (stop software PWM)
function PinModule.beepOff()
    PinModule.beep_running = false
    gpio.set(PinModule.pins.Beep, 0)
end

-- Start continuous beeper (must be stopped manually)
function PinModule.beepOn(frequency)
    frequency = frequency or 1000  -- Default 1kHz frequency

    -- Start a continuous software PWM in a task
    if sys and sys.taskInit then
        sys.taskInit(function()
            PinModule.beep_running = true
            gpio.setup(PinModule.pins.Beep, 0)

            -- Calculate timing
            local period_us = math.floor(1000000 / frequency)
            local high_time_ms = math.max(1, math.floor(period_us / 2000))  -- 50% duty cycle
            local low_time_ms = high_time_ms

            while PinModule.beep_running do
                gpio.set(PinModule.pins.Beep, 1)
                sys.wait(high_time_ms)
                gpio.set(PinModule.pins.Beep, 0)
                sys.wait(low_time_ms)
            end

            gpio.set(PinModule.pins.Beep, 0)  -- Ensure off when stopped
        end)
    end
end

-- Beep pattern function (beep multiple times) - using software PWM
function PinModule.beepPattern(count, beep_duration, pause_duration, frequency)
    count = count or 1
    beep_duration = beep_duration or 300  -- Default 300ms beep (longer = more audible)
    pause_duration = pause_duration or 250  -- Default 250ms pause
    frequency = frequency or 2000  -- Default 2kHz frequency (louder for passive buzzers)

    -- This function will be called from a task context where sys.wait is available
    for i = 1, count do
        -- Generate software PWM for beep
        PinModule.softwarePWM(frequency, beep_duration, 50)

        -- Add pause between beeps (except after the last beep)
        if i < count and sys and sys.wait then
            sys.wait(pause_duration)
        end
    end
end

return PinModule