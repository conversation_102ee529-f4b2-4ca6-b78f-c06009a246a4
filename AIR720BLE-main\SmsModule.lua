-- SmsModule.lua
-- Module to handle SMS-related functionality

local SmsModule = {}
local usermqtt = require("usermqtt") -- Import the usermqtt module
local my_utils = require("my_utils") -- Import the my_utils module
local variables = require("variables") -- Require shared variables
local SensorModule = require("SensorModule") -- Require SensorModule

-- Initialize SMS module
function SmsModule.init()
    -- Set up the necessary SMS configurations or initializations here
    -- For example, setting up callbacks or enabling the SMS receiver
    sms.setNewSmsCb(SmsModule.smsCallback) -- Register the callback function for incoming SMS
    log.info("SmsModule", "SMS Module Initialized")
end

-- Function to send an SMS
function SmsModule.sendSms(phoneNumber, message)
    log.info("SmsModule", "Sending SMS to: " .. phoneNumber .. " with message: " .. message)
    local result = sms.send(phoneNumber, message)
    if result then
        log.info("SmsModule", "SMS sent successfully")
    else
        log.error("SmsModule", "Failed to send SMS")
    end
end

-- Callback function to handle incoming SMS
function SmsModule.smsCallback(num, data, datetime)
    num = string.sub(num, -8) -- Get the last 8 digits of the phone number
    log.info("SmsModule", "Received SMS from: " .. num .. " with content: " .. data .. " at " .. datetime)

    -- Keywords to detect
    local keywords = {"Tand", "TG", "hugatsaa", "kod", "code", "opt"}

    -- Check if the SMS contains any of the keywords
    for _, keyword in ipairs(keywords) do
        if string.find(data, keyword) then
            -- Use the actual SMS content as the message
            local message = string.format('{"sms":"%s"}', data:gsub('"', '\\"'):gsub('\\', '\\\\'))

            -- Send to MQTT server using usermqtt module with error handling
            local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", message, 0)
            if not success then
                log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
            else
                log.info("SmsModule", "Message queued for MQTT server: " .. message)
            end
            return
        end
    end

    -- Existing logic for handling other commands
    if string.lower(data) == "restart" then
        log.info("SmsModule", "Received restart command, restarting the system...")
        SmsModule.sendSms(num, "System restarting...")
          -- Correctly pass the function to pcall without invoking it
            local r = 1  -- Use the appropriate restart argument based on your platform
            local success, err = pcall(function() sys.restart(r) end)
            if not success then
                log.error("commands", "failed to reboot: " .. (err or "Unknown error"))
            end
        return
    end

    -- Example: Handling MAC address configuration via SMS
    if string.find(data, "mac") then
        local mac_start = string.find(data, "mac") + 4
        local mac_end = string.find(data, "%s", mac_start) or (#data + 1)
        local mac_address = string.sub(data, mac_start, mac_end - 1)

        -- Validate the MAC address format
        if string.match(mac_address, "^%x%x:%x%x:%x%x:%x%x:%x%x:%x%x$") then
            vars.ble_mac_address = mac_address
            log.info("SmsModule", "MAC address configured: " .. vars.ble_mac_address)

            -- Save the BLE MAC address to a file
            if my_utils.writeToFile("/user_dir/ble.txt", vars.ble_mac_address) then
                SmsModule.sendSms(num, "MAC address configured successfully")
            else
                SmsModule.sendSms(num, "Failed to save MAC address")
            end
        else
            SmsModule.sendSms(num, "Invalid MAC address format")
        end

        return
    end

    -- New logic for handling messages in the format "unitel:88889999 1000"
    if string.match(data, "^unitel:%d%d%d%d%d%d%d%d %d+$") then
        local phoneNumber, unitAmount = string.match(data, "^unitel:(%d%d%d%d%d%d%d%d) (%d+)$")
        unitAmount = tonumber(unitAmount)
        if unitAmount and unitAmount <= 2000 then
            SmsModule.sendSms("1444", phoneNumber .. " " .. unitAmount)
        else
            SmsModule.sendSms(num, "dugaar 8 orontoi, Negj 2000 aas baga baih. ihdee 5 udaa")
        end
        return
    end

    if string.find(data, "x123456x1") then
        vars.phone_number1 = string.sub(data, string.find(data, "x123456x1") + string.len("x123456x1"), string.find(data, "xx") - 1)
        vars.phone_number1 = string.sub(vars.phone_number1, -8)
        log.info("Phone number change", vars.phone_number1)
        if string.len(vars.phone_number1) == 8 and my_utils.writeToFile("/user_dir/phone1.txt", vars.phone_number1) then
            SmsModule.sendSms(num, "success")
        else
            SmsModule.sendSms(num, "fail")
        end
        return
    end

    -- Add more command handling as needed
    -- Example: Handling additional phone numbers
    if string.find(data, "x123456x2") then
        vars.phone_number2 = string.sub(data, string.find(data, "x123456x2") + string.len("x123456x2"), string.find(data, "xx") - 1)
        vars.phone_number2 = string.sub(vars.phone_number2, -8)
        log.info("Phone number change", vars.phone_number2)
        if string.len(vars.phone_number2) == 8 and my_utils.writeToFile("/user_dir/phone2.txt", vars.phone_number2) then
            SmsModule.sendSms(num, "success")
        else
            SmsModule.sendSms(num, "fail")
        end
        return
    end
    if string.find(data, "x123456x3") then
        vars.phone_number3 = string.sub(data, string.find(data, "x123456x3") + string.len("x123456x3"), string.find(data, "xx") - 1)
        vars.phone_number3 = string.sub(vars.phone_number3, -8)
        log.info("Phone number change", vars.phone_number3)
        if string.len(vars.phone_number3) == 8 and my_utils.writeToFile("/user_dir/phone3.txt", vars.phone_number3) then
            SmsModule.sendSms(num, "success")
        else
            SmsModule.sendSms(num, "fail")
        end
        return
    end

      -- New logic for handling server configuration via SMS with a specific prefix
      if string.find(data, "SET_SERVER") then
        local serverKey, ip, port = string.match(data, "SET_SERVER (server%d):(%d+%.%d+%.%d+%.%d+):(%d+)")
        if serverKey and ip and port then
            port = tonumber(port)
            if port and port > 0 and port < 65536 then
                if usermqtt.servers[serverKey] then
                    -- Update the server details
                    usermqtt.servers[serverKey].ip = ip
                    usermqtt.servers[serverKey].port = port
                    usermqtt.setMqttServer(serverKey, ip, port)
                    log.info("SmsModule", "Server configuration updated: ", serverKey, ip, port)
                    SmsModule.sendSms(num, "Server configuration updated successfully")
                else
                    SmsModule.sendSms(num, "Invalid server key. Valid keys are: server1, server2, server3")
                end
            else
                SmsModule.sendSms(num, "Invalid port number")
            end
        else
            SmsModule.sendSms(num, "Invalid server configuration format")
        end
        return
    end

    -- Example: Handle unknown or unauthorized commands
    if num ~= vars.phone_number1 and num ~= vars.phone_number2 and num ~= vars.phone_number3 then
        -- Don't reply to automatic operator messages (phone numbers less than 8 digits)
        if string.len(num) >= 8 then
            SmsModule.sendSms(num, "taniulaagvi dugaar!")
            log.info("SmsModule", "Unauthorized SMS received from: " .. num)
        else
            log.info("SmsModule", "Automatic operator message received from: " .. num .. ", ignoring without reply")
        end
        return
    end
    -- You can add more SMS handling logic here
    vars.sms_data = data
    vars.callback_number = num
end

return SmsModule
