--- 模块功能：Enhanced logging and diagnostics
-- @module LogModule
-- <AUTHOR> Project
-- @license None
-- @copyright AIR780 Project
-- @release 2023.11.01

local LogModule = {}
local vars = require("variables")
local my_utils = require("my_utils")

-- Configuration
local LOG_BUFFER_SIZE = 100  -- Maximum number of log entries to keep
local LOG_FILE_PATH = "/user_dir/system_logs.txt"  -- Path to store persistent logs
local MAX_FILE_SIZE = 10240  -- Maximum log file size (10KB)

-- Initialize log buffer as a circular buffer
LogModule.logBuffer = {}
LogModule.logBufferIndex = 1  -- Current position in the circular buffer

-- Initialize diagnostic data
LogModule.diagnosticData = {
    bootTime = os.time(),
    lastResetReason = "",
    resetCount = 0,
    memoryStats = {},
    networkStats = {},
    batteryStats = {}
}

-- Load persistent data
local function loadPersistentData()
    -- Load reset count with error handling
    local resetCount = 0
    local success = false

    -- Try to read the reset count file
    if my_utils.fileExists("/user_dir/reset_count.txt") then
        local success, content = pcall(function()
            return io.readFile("/user_dir/reset_count.txt")
        end)

        if success and content then
            resetCount = tonumber(content) or 0
        end
    end

    -- Increment the reset count
    LogModule.diagnosticData.resetCount = resetCount + 1

    -- Try to save the reset count with error handling
    success = pcall(function()
        -- Make sure the directory exists - using file system functions available on AIR720U
        -- AIR720U may not have io.mkdir, so we'll try different approaches
        if not io.exists or not io.exists("/user_dir") then
            -- Try to create the directory using different methods
            pcall(function()
                -- Some versions use this method
                if rtos and rtos.make_dir then
                    rtos.make_dir("/user_dir")
                end
            end)
        end

        -- Write the file
        local file = io.open("/user_dir/reset_count.txt", "w")
        if file then
            file:write(tostring(LogModule.diagnosticData.resetCount))
            file:close()
            return true
        end
        return false
    end)

    if not success then
        log.warn("LogModule", "Failed to save reset count")
    end

    -- Try to determine last reset reason
    local resetReason = "Unknown"
    if rtos and rtos.get_reset_reason and type(rtos.get_reset_reason) == "function" then
        resetReason = rtos.get_reset_reason()
    end
    LogModule.diagnosticData.lastResetReason = resetReason

    -- Log the boot information
    log.info("LogModule", "System booted. Reset count: " .. LogModule.diagnosticData.resetCount ..
             ", Reset reason: " .. LogModule.diagnosticData.lastResetReason)
end

-- Initialize the module
function LogModule.init()
    loadPersistentData()

    -- Hook into the existing log system
    local originalLogInfo = log.info
    local originalLogWarn = log.warn
    local originalLogError = log.error

    -- Override log functions to capture logs
    log.info = function(tag, ...)
        LogModule.addLogEntry("INFO", tag, ...)
        return originalLogInfo(tag, ...)
    end

    log.warn = function(tag, ...)
        LogModule.addLogEntry("WARN", tag, ...)
        return originalLogWarn(tag, ...)
    end

    log.error = function(tag, ...)
        LogModule.addLogEntry("ERROR", tag, ...)
        return originalLogError(tag, ...)
    end

    -- Start periodic diagnostics collection
    sys.timerLoopStart(LogModule.collectDiagnostics, 60000)  -- Every minute

    -- Log initialization
    log.info("LogModule", "Enhanced logging system initialized")

    return true
end

-- Add a log entry to the circular buffer
function LogModule.addLogEntry(level, tag, ...)
    -- Combine all arguments into a single message
    local args = {...}
    local message = ""
    for i, v in ipairs(args) do
        message = message .. tostring(v) .. " "
    end
    message = message:match("^%s*(.-)%s*$") -- Trim whitespace

    -- Create log entry
    local entry = {
        timestamp = os.time(),
        level = level,
        tag = tag,
        message = message
    }

    -- Add to circular buffer
    LogModule.logBuffer[LogModule.logBufferIndex] = entry
    LogModule.logBufferIndex = (LogModule.logBufferIndex % LOG_BUFFER_SIZE) + 1

    -- Optionally write to persistent storage
    if level == "ERROR" then
        LogModule.appendLogToFile(entry)
    end
end

-- Append a log entry to the persistent log file
function LogModule.appendLogToFile(entry)
    -- Use pcall to catch any errors
    local success, err = pcall(function()
        -- Format the log entry
        local formattedEntry = string.format("[%d] %s [%s] %s: %s\r\n",
            entry.timestamp,
            os.date("%Y-%m-%d %H:%M:%S", entry.timestamp),
            entry.level,
            entry.tag,
            entry.message)

        -- Make sure the directory exists - using file system functions available on AIR720U
        -- AIR720U may not have io.mkdir, so we'll try different approaches
        if not io.exists or not io.exists("/user_dir") then
            -- Try to create the directory using different methods
            pcall(function()
                -- Some versions use this method
                if rtos and rtos.make_dir then
                    rtos.make_dir("/user_dir")
                end
            end)
        end

        -- Check if file exists and its size
        local fileSize = 0
        if io.exists(LOG_FILE_PATH) then
            local file = io.open(LOG_FILE_PATH, "r")
            if file then
                local content = file:read("*a") or ""
                fileSize = #content
                file:close()
            end
        end

        -- If file is too large, truncate it
        if fileSize > MAX_FILE_SIZE then
            local file = io.open(LOG_FILE_PATH, "w")
            if file then
                file:write("--- Log file truncated at " .. os.date("%Y-%m-%d %H:%M:%S") .. " ---\r\n")
                file:close()
            end
        end

        -- Append the log entry
        local file = io.open(LOG_FILE_PATH, "a+")
        if file then
            file:write(formattedEntry)
            file:close()
        else
            return false, "Failed to open log file for writing"
        end

        return true
    end)

    if not success then
        -- If there was an error, log it but don't try to write to file again
        -- to avoid infinite recursion
        print("LogModule: Error writing to log file: " .. (err or "Unknown error"))
    end
end

-- Collect system diagnostics
function LogModule.collectDiagnostics()
    -- Memory statistics
    LogModule.diagnosticData.memoryStats = {
        freeMemory = collectgarbage("count"),  -- Current memory usage in KB
        uptime = os.time() - LogModule.diagnosticData.bootTime  -- Uptime in seconds
    }

    -- Network statistics
    -- Safely check if functions exist before calling them
    local csq = 0
    local rssi = 0

    -- Try to get signal information based on available APIs
    local success = pcall(function()
        -- For AIR720U, we should use mobile APIs
        if mobile and type(mobile.csq) == "function" then
            -- AIR720U typically uses mobile.csq()
            csq = mobile.csq() or 0
            -- Convert CSQ to RSSI if needed
            if csq > 0 and csq <= 31 then
                rssi = -113 + (2 * csq)
            else
                rssi = -115  -- Default poor signal
            end
        elseif net and type(net.getCsq) == "function" then
            -- Some versions might have net.getCsq
            csq = net.getCsq() or 0
            rssi = net.getRssi() or -115
        elseif net and type(net.getRssi) == "function" then
            -- If only RSSI is available
            rssi = net.getRssi() or -115
            -- Calculate CSQ from RSSI
            csq = math.floor((rssi + 113) / 2)
            if csq < 0 then csq = 0 end
            if csq > 31 then csq = 31 end
        else
            -- Fallback if no signal APIs are available
            csq = 0
            rssi = -115
        end
    end)

    if not success then
        -- If there was an error, use default values
        csq = 0
        rssi = -115
    end

    -- Get operator information safely
    local operator = "Unknown"
    pcall(function()
        if mobile and type(mobile.getOperator) == "function" then
            operator = mobile.getOperator() or "Unknown"
        elseif net and type(net.getOperator) == "function" then
            operator = net.getOperator() or "Unknown"
        end
    end)

    -- Get SIM status safely
    local simStatus = false
    pcall(function()
        if sim and type(sim.getStatus) == "function" then
            simStatus = sim.getStatus() or false
        end
    end)

    -- Get socket status safely
    local socketReady = false
    pcall(function()
        if socket and type(socket.isReady) == "function" then
            socketReady = socket.isReady() or false
        end
    end)

    -- Get MQTT status safely
    local mqttReady = false
    pcall(function()
        if usermqtt and type(usermqtt.isReady) == "function" then
            mqttReady = usermqtt.isReady() or false
        end
    end)

    LogModule.diagnosticData.networkStats = {
        rssi = rssi,
        csq = csq,
        operator = operator,
        simStatus = simStatus,
        socketReady = socketReady,
        mqttReady = mqttReady
    }

    -- Battery statistics
    local voltage = SensorModule and SensorModule.readVoltage() or 0
    LogModule.diagnosticData.batteryStats = {
        voltage = voltage,
        timestamp = os.time()
    }

    -- Log periodic diagnostics summary
    log.info("Diagnostics", string.format(
        "Mem: %.1fKB, Uptime: %ds, RSSI: %d, Voltage: %.2fV",
        LogModule.diagnosticData.memoryStats.freeMemory,
        LogModule.diagnosticData.memoryStats.uptime,
        LogModule.diagnosticData.networkStats.rssi,
        LogModule.diagnosticData.batteryStats.voltage
    ))
end

-- Get recent logs as a formatted string
function LogModule.getFormattedLogs(count, levelFilter)
    count = count or LOG_BUFFER_SIZE
    count = math.min(count, LOG_BUFFER_SIZE)

    local result = "=== System Logs ===\n"
    local entries = {}

    -- Collect valid entries from the circular buffer
    for i = 1, LOG_BUFFER_SIZE do
        local entry = LogModule.logBuffer[i]
        if entry and (not levelFilter or entry.level == levelFilter) then
            table.insert(entries, entry)
        end
    end

    -- Sort entries by timestamp (newest first)
    table.sort(entries, function(a, b) return a.timestamp > b.timestamp end)

    -- Format the entries
    for i = 1, math.min(count, #entries) do
        local entry = entries[i]
        result = result .. string.format("[%s] %s: %s\n",
            entry.level,
            entry.tag,
            entry.message)
    end

    return result
end

-- Get system diagnostics as a formatted string
function LogModule.getFormattedDiagnostics()
    local result = "=== System Diagnostics ===\n"

    -- System information
    result = result .. string.format("Device: AIR720U\n")
    result = result .. string.format("IMEI: %s\n", misc.getImei())
    result = result .. string.format("Firmware: %s\n", VERSION or "Unknown")
    result = result .. string.format("Reset Count: %d\n", LogModule.diagnosticData.resetCount)
    result = result .. string.format("Last Reset: %s\n", LogModule.diagnosticData.lastResetReason)
    result = result .. string.format("Uptime: %d seconds\n", LogModule.diagnosticData.memoryStats.uptime or 0)

    -- Memory information
    result = result .. string.format("Memory: %.1f KB\n", LogModule.diagnosticData.memoryStats.freeMemory or 0)

    -- Network information
    result = result .. string.format("Signal: %d dBm (CSQ: %d)\n",
        LogModule.diagnosticData.networkStats.rssi or 0,
        LogModule.diagnosticData.networkStats.csq or 0)
    result = result .. string.format("Operator: %s\n", LogModule.diagnosticData.networkStats.operator or "Unknown")
    result = result .. string.format("SIM: %s\n", LogModule.diagnosticData.networkStats.simStatus and "OK" or "Not detected")
    result = result .. string.format("Socket: %s\n", LogModule.diagnosticData.networkStats.socketReady and "Ready" or "Not ready")
    result = result .. string.format("MQTT: %s\n", LogModule.diagnosticData.networkStats.mqttReady and "Connected" or "Disconnected")

    -- Battery information
    result = result .. string.format("Voltage: %.2f V\n", LogModule.diagnosticData.batteryStats.voltage or 0)

    return result
end

-- Get logs via MQTT
function LogModule.sendLogsViaMqtt(count, levelFilter)
    local logs = LogModule.getFormattedLogs(count, levelFilter)
    local diagnostics = LogModule.getFormattedDiagnostics()
    local message = diagnostics .. "\n\n" .. logs

    -- Send via MQTT using the safe send function
    local success, err = my_utils.safeMqttSend(usermqtt.clientID() .. "/logs", message, 0, 3, 2000)
    if not success then
        log.error("LogModule", "Failed to send logs via MQTT: " .. (err or "Unknown error"))
        return false
    end

    log.info("LogModule", "Logs sent via MQTT")
    return true
end

-- Get logs via SMS
function LogModule.sendLogsViaSms(phoneNumber, count)
    count = count or 5  -- Default to last 5 logs for SMS to keep message size reasonable

    -- Get only ERROR logs for SMS to keep message size small
    local logs = LogModule.getFormattedLogs(count, "ERROR")
    local diagnostics = "Device: " .. misc.getImei() ..
                        ", Mem: " .. math.floor(LogModule.diagnosticData.memoryStats.freeMemory or 0) .. "KB" ..
                        ", Volt: " .. string.format("%.1f", LogModule.diagnosticData.batteryStats.voltage or 0) .. "V"

    local message = diagnostics .. "\n" .. logs

    -- Truncate if too long for SMS
    if #message > 160 then
        message = string.sub(message, 1, 157) .. "..."
    end

    -- Send via SMS
    local success, err = pcall(sms.send, phoneNumber, message)
    if not success then
        log.error("LogModule", "Failed to send logs via SMS: " .. (err or "Unknown error"))
        return false
    end

    log.info("LogModule", "Logs sent via SMS to " .. phoneNumber)
    return true
end

-- Clear the log buffer
function LogModule.clearLogs()
    LogModule.logBuffer = {}
    LogModule.logBufferIndex = 1
    log.info("LogModule", "Log buffer cleared")
    return true
end

return LogModule
