--- 模块功能: BLE设备驱动
-- @module bleMaster
-- <AUTHOR>
-- @license None
-- @copyright Yun.
-- @release 2023.01.27
module(..., package.seeall)

require 'sys'
require 'log'

local function BLE_Event_Callback(msg)
    if msg.event == btcore.MSG_OPEN_CNF then
        log.info("BLE", "BLE Open Success") -- 蓝牙打开成功
        sys.publish("BLE_OPEN", msg.result) -- 发布时间
    elseif msg.event == btcore.MSG_CLOSE_CNF then
        log.info("BLE", "BLE Close") -- 蓝牙关闭成功
    elseif msg.event == btcore.MSG_BLE_CONNECT_CNF then
        sys.publish("BLE_CONNECTED", {
            ["handle"] = msg.handle,
            ["result"] = msg.result,
            ["addr"] = msg.addr
        }) -- 蓝牙连接成功
    elseif msg.event == btcore.MSG_BLE_DISCONNECT_CNF then
        log.info("BLE", "BLE Disconnect") -- 蓝牙断开连接
    elseif msg.event == btcore.MSG_BLE_DATA_IND then
        sys.publish("BLE_RECEIVER", {
            ["data"] = msg.data,
            ["uuid"] = msg.uuid,
            ["len"] = msg.len
        }) -- 接收到的数据内容
    elseif msg.event == btcore.MSG_BLE_SCAN_CNF then
        if (msg.enable == 1) then
            sys.publish("BLE_SCAN_OPEN_CNF", msg.result) -- 打开扫描成功
        else
            sys.publish("BLE_SCAN_CLOSE_CNF", msg.result) -- 关闭扫描成功
        end
    elseif msg.event == btcore.MSG_BLE_SCAN_IND then
        sys.publish("BLE_SCAN_IND", {
            ["name"] = msg.name,
            ["addr_type"] = msg.addr_type,
            ["addr"] = msg.addr,
            ["manu_data"] = msg.manu_data,
            ["raw_data"] = msg.raw_data,
            ["raw_len"] = msg.raw_len,
            ["rssi"] = msg.rssi
        }) -- 接收到扫描广播包数据
    elseif msg.event == btcore.MSG_BLE_FIND_CHARACTERISTIC_IND then
        sys.publish("BLE_FIND_CHARACTERISTIC_IND", msg.result) -- 发现服务包含的特征
    elseif msg.event == btcore.MSG_BLE_FIND_SERVICE_IND then
        log.info("BLE", "Find Service UUID", msg.uuid) -- 发现蓝牙包含的16bit uuid
        if msg.uuid == 0xfff0 then -- 如果找到了需要的UUID
            sys.publish("BLE_FIND_SERVICE_IND", msg.result) -- 停止发现
        end
    elseif msg.event == btcore.MSG_BLE_FIND_CHARACTERISTIC_UUID_IND then
        log.info("BLE", "Find Characteristic UUID", msg.uuid) -- 发现到服务内包含的特征uuid
    elseif msg.event == btcore.MSG_BLE_READ_VALUE_IND then
        log.info("BLE", "Read Characteristic Value", msg.data) -- 读特征value值
    end
end

function BLE_Init()
    log.info("BLE", "Register Event Function") -- 打印调试信息
    rtos.on(rtos.MSG_BLUETOOTH, BLE_Event_Callback) -- 注册事件回调函数
end

function BLE_Read_Data(deviceName, payload)
    -- Helper function to initiate BLE scan and find device by name
    local function findDeviceByName(targetName)
        log.info("BLE", "Starting BLE scan to find device: " .. targetName)
        btcore.scan(1)
        local foundDevice = nil

        while true do
            local _, scanResult = sys.waitUntil("BLE_SCAN_IND", 10000)
            if scanResult then
                local name = scanResult.name
                local addr = scanResult.addr
                local addr_type = scanResult.addr_type

                log.info("BLE", "Discovered Device: " .. name .. " with MAC: " .. addr)

                if name == targetName then
                    foundDevice = {addr = addr, addr_type = addr_type}
                    log.info("BLE", "Target device found: " .. name .. " with MAC: " .. addr)
                    break
                end
            else
                log.warn("BLE", "No devices found, retrying...")
                return nil
            end
        end

        btcore.scan(0)
        return foundDevice
    end

    -- Validate the payload
    if type(payload) ~= "table" then
        log.warn("BLE", "Invalid payload format")
        return -1
    end

    -- Power on Bluetooth and set it to master mode
    btcore.open(1)
    log.info("BLE", "Opened BLE master mode")
    local _, result = sys.waitUntil("BLE_OPEN", 5000)
    if result == nil or result ~= 0 then
        log.error("BLE", "Failed to open BLE master mode")
        return -1
    end

    -- Find the device by name
    local device = findDeviceByName(deviceName)
    if not device then
        log.error("BLE", "Device not found: " .. deviceName)
        btcore.close()
        return -1
    end

    -- Attempt to connect to the found device
    log.info("BLE", "Connecting to device with MAC: " .. device.addr)
    btcore.connect(device.addr, device.addr_type)
    local _, slaveDevice = sys.waitUntil("BLE_CONNECTED", 5000)
    if slaveDevice == nil or slaveDevice.result ~= 0 then
        log.error("BLE", "Failed to connect to device with MAC: " .. device.addr)
        btcore.close()
        return -1
    end
    log.info("BLE", "Connected to slave device with MAC: " .. device.addr)

    -- Discover all 16-bit service UUIDs
    log.info("BLE", "Discovering 16-bit service UUIDs")
    btcore.findservice()
    _, result = sys.waitUntil("BLE_FIND_SERVICE_IND", 5000)
    if not result then
        log.error("BLE", "Failed to find service UUID on device")
        btcore.disconnect(slaveDevice.handle)
        btcore.close()
        return -1
    end

    -- Discover the characteristic UUID
    log.info("BLE", "Discovering characteristic UUID 0xfff0")
    btcore.findcharacteristic(0xfff0)
    _, result = sys.waitUntil("BLE_FIND_CHARACTERISTIC_IND", 5000)
    if not result then
        log.error("BLE", "Failed to find characteristic UUID 0xfff0 on device")
        btcore.disconnect(slaveDevice.handle)
        btcore.close()
        return -1
    end

    -- Enable notification for the characteristic
    log.info("BLE", "Enabling notification for characteristic 0xfff1")
    btcore.opennotification(0xfff1, slaveDevice.handle)

    -- Convert the payload table to a hex string
    local payloadHex = ""
    for i = 1, #payload do
        payloadHex = payloadHex .. string.format("%02X", payload[i])
    end
    log.info("BLE", "Hex Payload to be sent: " .. payloadHex)

    -- Send the hex string payload
    log.info("BLE", "Sending data to device")
    local sendResult = btcore.send(payloadHex, 0xfff1, slaveDevice.handle, 1)
    if sendResult then
        log.info("BLE", "Data sent successfully in hex format")
    else
        log.error("BLE", "Failed to send data in hex format to device")
        btcore.disconnect(slaveDevice.handle)
        btcore.close()
        return -1
    end

    -- Wait for and process the response
    log.info("BLE", "Waiting for response from device")
    _, result = sys.waitUntil("BLE_RECEIVER", 5000)
    if not result then
        log.error("BLE", "Failed to receive data from device")
        btcore.disconnect(slaveDevice.handle)
        btcore.close()
        return -1
    end
    log.info("BLE", "Received response from device")

    -- Read received data
    local receiverUUID = ""
    local receiverData = ""
    local receiverLen = 0

    while true do
        local recvuuid, recvdata, recvlen = btcore.recv(2)
        if recvlen == 0 then break end
        receiverUUID = recvuuid
        receiverLen = receiverLen + recvlen
        receiverData = receiverData .. recvdata
    end

    -- Handle received data
    if receiverLen ~= 0 then
        log.info("BLE", "Received UUID:", string.toHex(receiverUUID, " "), "Len:", receiverLen, "Data:", string.toHex(receiverData, " "))
    else
        log.warn("BLE", "No data received from device")
    end

    -- Disconnect and close Bluetooth connection
    log.info("BLE", "Disconnecting from device and closing BLE connection")
    btcore.disconnect(slaveDevice.handle)
    btcore.close()

    return 0 -- Return success status
end

