-- my_utils.lua - Simplified utility functions for Air780EG
local my_utils = {}
local vars = require('variables')

function my_utils.fileExists(path)
    local file = io.open(path, "r")
    if file then file:close(); return true end
    return false
end

function my_utils.readFile(path)
    local file = io.open(path, "r")
    if not file then return nil end
    local content = file:read("*a")
    file:close()
    return content
end

function my_utils.writeToFile(path, content)
    local file = io.open(path, "w")
    if not file then return false end
    file:write(tostring(content))
    file:close()
    return true
end

function my_utils.formatNumber(num, decimals)
    decimals = decimals or 2
    local mult = 10^decimals
    return math.floor(num * mult + 0.5) / mult
end

-- Load configuration from files
function my_utils.loadConfiguration()
    -- Load phone numbers
    if my_utils.fileExists("/user_dir/phone1.txt") then
        vars.phone_number1 = my_utils.readFile("/user_dir/phone1.txt")
    end
    if my_utils.fileExists("/user_dir/phone2.txt") then
        vars.phone_number2 = my_utils.readFile("/user_dir/phone2.txt")
    end
    if my_utils.fileExists("/user_dir/phone3.txt") then
        vars.phone_number3 = my_utils.readFile("/user_dir/phone3.txt")
    end

    -- Load basic settings
    if my_utils.fileExists("/user_dir/volt_offset.txt") then
        vars.voltage_offset = tonumber(my_utils.readFile("/user_dir/volt_offset.txt")) or 0
    end
    if my_utils.fileExists("/user_dir/license.txt") then
        vars.isLicensed = my_utils.readFile("/user_dir/license.txt") == "true"
    else
        vars.isLicensed = true
    end
end

return my_utils
